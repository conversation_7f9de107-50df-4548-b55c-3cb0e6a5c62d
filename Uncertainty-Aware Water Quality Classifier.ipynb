{"cells": [{"cell_type": "markdown", "id": "e79b46b6", "metadata": {}, "source": ["Load dataset"]}, {"cell_type": "code", "execution_count": 1, "id": "b0ee57c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 3276 entries, 0 to 3275\n", "Data columns (total 10 columns):\n", " #   Column           Non-Null Count  Dtype  \n", "---  ------           --------------  -----  \n", " 0   ph               2785 non-null   float64\n", " 1   Hardness         3276 non-null   float64\n", " 2   Solids           3276 non-null   float64\n", " 3   Chloramines      3276 non-null   float64\n", " 4   Sulfate          2495 non-null   float64\n", " 5   Conductivity     3276 non-null   float64\n", " 6   Organic_carbon   3276 non-null   float64\n", " 7   Trihalomethanes  3114 non-null   float64\n", " 8   Turbidity        3276 non-null   float64\n", " 9   Potability       3276 non-null   int64  \n", "dtypes: float64(9), int64(1)\n", "memory usage: 256.1 KB\n", "None\n", "\n", "Missing values:\n", " ph                 491\n", "Hardness             0\n", "Solids               0\n", "Chloramines          0\n", "Sulfate            781\n", "Conductivity         0\n", "Organic_carbon       0\n", "Trihalomethanes    162\n", "Turbidity            0\n", "Potability           0\n", "dtype: int64\n", "\n", "Preview:\n", "          ph    Hardness        Solids  Chloramines     Sulfate  Conductivity  \\\n", "0       NaN  204.890455  20791.318981     7.300212  368.516441    564.308654   \n", "1  3.716080  129.422921  18630.057858     6.635246         NaN    592.885359   \n", "2  8.099124  224.236259  19909.541732     9.275884         NaN    418.606213   \n", "3  8.316766  214.373394  22018.417441     8.059332  356.886136    363.266516   \n", "4  9.092223  181.101509  17978.986339     6.546600  310.135738    398.410813   \n", "\n", "   Organic_carbon  Trihalomethanes  Turbidity  Potability  \n", "0       10.379783        86.990970   2.963135           0  \n", "1       15.180013        56.329076   4.500656           0  \n", "2       16.868637        66.420093   3.055934           0  \n", "3       18.436524       100.341674   4.628771           0  \n", "4       11.558279        31.997993   4.075075           0  \n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv(\"water_potability.csv\")\n", "\n", "# Display basic info\n", "print(df.info())\n", "print(\"\\nMissing values:\\n\", df.isnull().sum())\n", "print(\"\\nPreview:\\n\", df.head())\n"]}, {"cell_type": "markdown", "id": "9a8fad39", "metadata": {}, "source": ["Median Imputation (Baseline)"]}, {"cell_type": "code", "execution_count": 2, "id": "d150b59b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ph                 0\n", "Hardness           0\n", "Solids             0\n", "Chloramines        0\n", "Sulfate            0\n", "Conductivity       0\n", "Organic_carbon     0\n", "Trihalomethanes    0\n", "Turbidity          0\n", "Potability         0\n", "dtype: int64\n"]}], "source": ["df_clean = df.copy()\n", "\n", "# Median imputation for missing values\n", "df_clean.fillna(df_clean.median(numeric_only=True), inplace=True)\n", "\n", "# Confirm that missing values are gone\n", "print(df_clean.isnull().sum())\n"]}, {"cell_type": "markdown", "id": "5d79613a", "metadata": {}, "source": ["Outlier Detection"]}, {"cell_type": "code", "execution_count": 3, "id": "c855a1e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                ph     Hardness        Solids  Chloramines      Sulfate  \\\n", "count  3276.000000  3276.000000   3276.000000  3276.000000  3276.000000   \n", "mean      7.074194   196.369496  22014.092526     7.122277   333.608364   \n", "std       1.470040    32.879761   8768.570828     1.583085    36.143851   \n", "min       0.000000    47.432000    320.942611     0.352000   129.000000   \n", "25%       6.277673   176.850538  15666.690297     6.127421   317.094638   \n", "50%       7.036752   196.967627  20927.833607     7.130299   333.073546   \n", "75%       7.870050   216.667456  27332.762127     8.114887   350.385756   \n", "max      14.000000   323.124000  61227.196008    13.127000   481.030642   \n", "\n", "       Conductivity  Organic_carbon  Trihalomethanes    Turbidity   Potability  \n", "count   3276.000000     3276.000000      3276.000000  3276.000000  3276.000000  \n", "mean     426.205111       14.284970        66.407478     3.966786     0.390110  \n", "std       80.824064        3.308162        15.769958     0.780382     0.487849  \n", "min      181.483754        2.200000         0.738000     1.450000     0.000000  \n", "25%      365.734414       12.065801        56.647656     3.439711     0.000000  \n", "50%      421.884968       14.218338        66.622485     3.955028     0.000000  \n", "75%      481.792304       16.557652        76.666609     4.500320     1.000000  \n", "max      753.342620       28.300000       124.000000     6.739000     1.000000  \n", "\n", "Unusual pH values:\n", "            ph  Potability\n", "80    1.844538           0\n", "104   2.612036           0\n", "354   2.798549           1\n", "692   1.757037           1\n", "726   0.227499           1\n", "810   0.989912           1\n", "1231  2.690831           0\n", "1343  2.569244           0\n", "2165  2.803563           0\n", "2189  2.558103           0\n", "2300  2.974429           1\n", "2343  2.538116           1\n", "2473  2.945469           0\n", "2681  2.376768           0\n", "2899  1.431782           0\n", "2928  0.975578           0\n", "2932  2.925174           0\n", "3014  0.000000           0\n", "3088  2.128531           0\n", "3094  1.985383           0\n"]}], "source": ["# See min/max values for each column\n", "print(df_clean.describe())\n", "\n", "# Specifically inspect edge cases in pH\n", "print(\"\\nUnusual pH values:\")\n", "print(df_clean[df_clean[\"ph\"] < 3][[\"ph\", \"Potability\"]])  # ph < 3 is usually not potable\n"]}, {"cell_type": "markdown", "id": "afe72ec0", "metadata": {}, "source": [" Clip Extreme pH Values"]}, {"cell_type": "markdown", "id": "7b176061", "metadata": {}, "source": ["To reflect practical field conditions and embedded calibration thresholds, we applied value clipping to pH readings below 3.0, which are chemically implausible and likely result from low-cost sensor error."]}, {"cell_type": "code", "execution_count": 4, "id": "dfb154c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extreme pH values before clip: 20\n", "Extreme pH values after clip: 0\n", "New min and max pH: 3.0 13.999999999999998\n"]}], "source": ["# Count rows where pH is below 3 before clipping\n", "print(\"Extreme pH values before clip:\", (df_clean[\"ph\"] < 3).sum())\n", "\n", "# Clip pH values to a reasonable sensor-safe range\n", "df_clean[\"ph\"] = df_clean[\"ph\"].clip(lower=3.0, upper=14.0)\n", "\n", "# Confirm after clipping\n", "print(\"Extreme pH values after clip:\", (df_clean[\"ph\"] < 3).sum())\n", "print(\"New min and max pH:\", df_clean[\"ph\"].min(), df_clean[\"ph\"].max())\n"]}, {"cell_type": "markdown", "id": "45b0c090", "metadata": {}, "source": ["Scaling / Normalization Check"]}, {"cell_type": "code", "execution_count": 5, "id": "14bb83fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                ph     Hardness        Solids  Chloramines      Sulfate  \\\n", "count  3276.000000  3276.000000   3276.000000  3276.000000  3276.000000   \n", "mean      7.079953   196.369496  22014.092526     7.122277   333.608364   \n", "std       1.450477    32.879761   8768.570828     1.583085    36.143851   \n", "min       3.000000    47.432000    320.942611     0.352000   129.000000   \n", "25%       6.277673   176.850538  15666.690297     6.127421   317.094638   \n", "50%       7.036752   196.967627  20927.833607     7.130299   333.073546   \n", "75%       7.870050   216.667456  27332.762127     8.114887   350.385756   \n", "max      14.000000   323.124000  61227.196008    13.127000   481.030642   \n", "\n", "       Conductivity  Organic_carbon  Trihalomethanes    Turbidity   Potability  \n", "count   3276.000000     3276.000000      3276.000000  3276.000000  3276.000000  \n", "mean     426.205111       14.284970        66.407478     3.966786     0.390110  \n", "std       80.824064        3.308162        15.769958     0.780382     0.487849  \n", "min      181.483754        2.200000         0.738000     1.450000     0.000000  \n", "25%      365.734414       12.065801        56.647656     3.439711     0.000000  \n", "50%      421.884968       14.218338        66.622485     3.955028     0.000000  \n", "75%      481.792304       16.557652        76.666609     4.500320     1.000000  \n", "max      753.342620       28.300000       124.000000     6.739000     1.000000  \n"]}], "source": ["# See min, max, and standard deviation per column\n", "print(df_clean.describe())\n"]}, {"cell_type": "markdown", "id": "31925941", "metadata": {}, "source": ["Distance-based models (like KNN, NGBoost, quantile forests) need scaling so well make a df scaled version"]}, {"cell_type": "code", "execution_count": 6, "id": "f7b311a1", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ph", "rawType": "float64", "type": "float"}, {"name": "Hardness", "rawType": "float64", "type": "float"}, {"name": "Solids", "rawType": "float64", "type": "float"}, {"name": "Chloramines", "rawType": "float64", "type": "float"}, {"name": "Sulfate", "rawType": "float64", "type": "float"}, {"name": "Conductivity", "rawType": "float64", "type": "float"}, {"name": "Organic_carbon", "rawType": "float64", "type": "float"}, {"name": "Trihalomethanes", "rawType": "float64", "type": "float"}, {"name": "Turbidity", "rawType": "float64", "type": "float"}, {"name": "Potability", "rawType": "int64", "type": "integer"}], "ref": "5dec84f8-804b-42d8-80a4-74a5acd2c8c4", "rows": [["0", "-0.029788354408036795", "0.25919471072588524", "-0.1394708713751093", "0.11241484558326897", "0.9659569987660399", "1.708954233030653", "-1.1806505657521278", "1.3054337277832433", "-1.2862975839507313", "0"], ["1", "-2.3195040595155763", "-2.0364136650369358", "-0.385986649553349", "-0.3076937081044106", "-0.014799206837658466", "2.062574999579364", "0.27059723987304235", "-0.6391862837121229", "0.6842178911400506", "0"], ["2", "0.7027531395782498", "0.8476648329496834", "-0.2400473367970158", "1.3605938595071565", "-0.014799206837658466", "-0.09403211476423624", "0.7811168574507188", "0.0008000125863123948", "-1.1673654621705243", "0"], ["3", "0.8528244512486047", "0.5476513738775178", "0.0004933044420881189", "0.5920078206187585", "0.64412961134175", "-0.7788299619632442", "1.2551344298168203", "2.1521539541519092", "0.8484115203631912", "0"], ["4", "1.3875286816625507", "-0.46442908670804023", "-0.46024857027889415", "-0.36369793312202997", "-0.6495215502874558", "-0.34393890863273974", "-0.824357169283179", "-2.182297472909101", "0.1387855311835096", "0"]], "shape": {"columns": 10, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ph</th>\n", "      <th>Hardness</th>\n", "      <th>Solids</th>\n", "      <th>Chloramines</th>\n", "      <th>Sulfate</th>\n", "      <th>Conductivity</th>\n", "      <th>Organic_carbon</th>\n", "      <th>Trihalomethanes</th>\n", "      <th>Turbidity</th>\n", "      <th>Potability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-0.029788</td>\n", "      <td>0.259195</td>\n", "      <td>-0.139471</td>\n", "      <td>0.112415</td>\n", "      <td>0.965957</td>\n", "      <td>1.708954</td>\n", "      <td>-1.180651</td>\n", "      <td>1.305434</td>\n", "      <td>-1.286298</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-2.319504</td>\n", "      <td>-2.036414</td>\n", "      <td>-0.385987</td>\n", "      <td>-0.307694</td>\n", "      <td>-0.014799</td>\n", "      <td>2.062575</td>\n", "      <td>0.270597</td>\n", "      <td>-0.639186</td>\n", "      <td>0.684218</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.702753</td>\n", "      <td>0.847665</td>\n", "      <td>-0.240047</td>\n", "      <td>1.360594</td>\n", "      <td>-0.014799</td>\n", "      <td>-0.094032</td>\n", "      <td>0.781117</td>\n", "      <td>0.000800</td>\n", "      <td>-1.167365</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.852824</td>\n", "      <td>0.547651</td>\n", "      <td>0.000493</td>\n", "      <td>0.592008</td>\n", "      <td>0.644130</td>\n", "      <td>-0.778830</td>\n", "      <td>1.255134</td>\n", "      <td>2.152154</td>\n", "      <td>0.848412</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.387529</td>\n", "      <td>-0.464429</td>\n", "      <td>-0.460249</td>\n", "      <td>-0.363698</td>\n", "      <td>-0.649522</td>\n", "      <td>-0.343939</td>\n", "      <td>-0.824357</td>\n", "      <td>-2.182297</td>\n", "      <td>0.138786</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         ph  Hardness    Solids  Chloramines   Sulfate  Conductivity  \\\n", "0 -0.029788  0.259195 -0.139471     0.112415  0.965957      1.708954   \n", "1 -2.319504 -2.036414 -0.385987    -0.307694 -0.014799      2.062575   \n", "2  0.702753  0.847665 -0.240047     1.360594 -0.014799     -0.094032   \n", "3  0.852824  0.547651  0.000493     0.592008  0.644130     -0.778830   \n", "4  1.387529 -0.464429 -0.460249    -0.363698 -0.649522     -0.343939   \n", "\n", "   Organic_carbon  Trihalomethanes  Turbidity  Potability  \n", "0       -1.180651         1.305434  -1.286298           0  \n", "1        0.270597        -0.639186   0.684218           0  \n", "2        0.781117         0.000800  -1.167365           0  \n", "3        1.255134         2.152154   0.848412           0  \n", "4       -0.824357        -2.182297   0.138786           0  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.preprocessing import StandardScaler\n", "\n", "# Create scaler and scale everything except Potability\n", "scaler = StandardScaler()\n", "scaled_features = scaler.fit_transform(df_clean.drop(\"Potability\", axis=1))\n", "\n", "# Reassemble into a DataFrame\n", "df_scaled = pd.DataFrame(scaled_features, columns=df_clean.columns[:-1])\n", "df_scaled[\"Potability\"] = df_clean[\"Potability\"].values\n", "\n", "# Show first few rows of scaled data\n", "df_scaled.head()\n"]}, {"cell_type": "markdown", "id": "f4973245", "metadata": {}, "source": ["#Train Random Forest Classifier (Baseline)"]}, {"cell_type": "code", "execution_count": 7, "id": "2c2afe5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Random Forest Performance:\n", "Accuracy: 0.6494\n", "F1 Score: 0.3915\n", "ROC AUC: 0.6423\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.66      0.88      0.75       400\n", "           1       0.61      0.29      0.39       256\n", "\n", "    accuracy                           0.65       656\n", "   macro avg       0.63      0.58      0.57       656\n", "weighted avg       0.64      0.65      0.61       656\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 128300 (\\N{MICROSCOPE}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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***************************/XuX4a1bt+r8+fPpCpt+fn6aN2+ewsLCtHv3brVr105OTk7pqq9Xr166du2a2rZtq7179+rUqVNas2aNOnXqpMTERO3evVujR4/Wvn37dPbsWS1ZskSXL19WuXLl0rUdAAAAAEiLJzqsFixYUCEhIfr5559Vvnx5ffbZZxo/fvwD16lWrZp++uknLVy4UBUrVtTQoUM1YsSIB95BOC0qVaqkLVu26OTJk6pfv76qVq2qoUOHysvLy9JnxIgRioiIUMmSJVWwYME0j/3NN9/o+vXrqlatmtq3b6++ffuqUKFC6arPy8tLO3bsUGJiop577jn5+/urX79+8vDwUK5cueTm5qatW7eqefPmKl26tAYPHqwJEyaoWbNm6doOAAAAAKSFyUjt2SNAJoqJiZG7u7sq95klO3P6ZnwBAAAy2/5xHbK7BCDHSs4G0dHRcnNze2DfJ3pmFQAAAADw30RYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzSGsAgAAAABsDmEVAAAAAGBzCKsAAAAAAJtDWAUAAAAA2Bz77C4AOcfWUW3l5uaW3WUAAAAA+A9gZhUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHL6ziixnGIYkKSYmJpsrAQAAAJCdkjNBckZ4EMIqstzVq1clSd7e3tlcCQAAAABbcOPGDbm7uz+wD2EVWS5fvnySpLNnzz70AwnbFhMTI29vb507d447O/+HcR6fHJzLJwPn8cnBuXwycB6zlmEYunHjhry8vB7al7CKLJcr179fjXZ3d+cX/gnh5ubGuXwCcB6fHJzLJwPn8cnBuXwycB6zTlonsLjBEgAAAADA5hBWAQAAAAA2h7CKLGc2m/XJJ5/IbDZndyl4RJzLJwPn8cnBuXwycB6fHJzLJwPn0XaYjLTcMxgAAAAAgMeImVUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbQ1hFhsyYMUO+vr5ydHRU7dq1tWfPngf2//nnn1W2bFk5OjrK399fK1eutFpuGIaGDh2qIkWKyMnJSY0bN1Z4eHhW7gKUuecxISFBH3zwgfz9/eXs7CwvLy916NBBf//9d1bvBpT5v5N369Gjh0wmkyZPnpzJVeNeWXEew8LC9OKLL8rd3V3Ozs6qWbOmzp49m1W7gP8vs89lbGysevfuraeeekpOTk4qX768Zs2alZW7AKXvPP7xxx9q3bq1fH19H/hnZno/G8gcmX0ux4wZo5o1a8rV1VWFChVSq1atdOLEiSzcgxzKANJp4cKFhoODg/Htt98af/zxh9GtWzfDw8PDuHjxYqr9d+zYYdjZ2Rljx441jh07ZgwePNjInTu3ceTIEUufzz77zHB3dzeWLl1qHDp0yHjxxReN4sWLG//888/j2q0cJ7PPY1RUlNG4cWPjxx9/NI4fP27s3LnTqFWrllG9evXHuVs5Ulb8TiZbsmSJUblyZcPLy8uYNGlSFu9JzpYV5/F///ufkS9fPmPgwIHGgQMHjP/973/GsmXL7jsmMkdWnMtu3boZJUuWNDZt2mScOXPG+PLLLw07Oztj2bJlj2u3cpz0nsc9e/YYAwYMMBYsWGB4enqm+mdmesdE5siKcxkUFGTMmTPHOHr0qBEaGmo0b97cKFasmBEbG5vFe5OzEFaRbrVq1TJ69epleZ+YmGh4eXkZY8aMSbV/mzZtjOeff96qrXbt2sZbb71lGIZhJCUlGZ6ensa4ceMsy6Oiogyz2WwsWLAgC/YAhpH55zE1e/bsMSQZf/75Z+YUjVRl1bn866+/jKJFixpHjx41fHx8CKtZLCvO42uvvWa8+eabWVMw7isrzmWFChWMESNGWPWpVq2a8fHHH2di5bhbes/j3e73Z+ajjImMy4pzea9Lly4ZkowtW7Y8Sqm4B5cBI13i4+O1f/9+NW7c2NKWK1cuNW7cWDt37kx1nZ07d1r1l6SgoCBL/zNnzujChQtWfdzd3VW7du37jolHkxXnMTXR0dEymUzy8PDIlLqRUlady6SkJLVv314DBw5UhQoVsqZ4WGTFeUxKStJvv/2m0qVLKygoSIUKFVLt2rW1dOnSLNsPZN3vZN26dbV8+XKdP39ehmFo06ZNOnnypJ577rms2ZEcLiPnMTvGxMM9ruMeHR0tScqXL1+mjQm+s4p0unLlihITE1W4cGGr9sKFC+vChQuprnPhwoUH9k/+b3rGxKPJivN4r9u3b+uDDz5Q27Zt5ebmljmFI4WsOpeff/657O3t1bdv38wvGilkxXm8dOmSYmNj9dlnn6lp06Zau3atXnrpJb388svasmVL1uwIsux3ctq0aSpfvryeeuopOTg4qGnTppoxY4YaNGiQ+TuBDJ3H7BgTD/c4jntSUpL69eungIAAVaxYMVPGxL/ss7sAAE+ehIQEtWnTRoZh6IsvvsjucpBO+/fv15QpU3TgwAGZTKbsLgcZlJSUJElq2bKl3n33XUlSlSpV9Pvvv2vWrFkKDAzMzvKQTtOmTdOuXbu0fPly+fj4aOvWrerVq5e8vLxSzMoCeLx69eqlo0ePavv27dldyhOHmVWkS4ECBWRnZ6eLFy9atV+8eFGenp6pruPp6fnA/sn/Tc+YeDRZcR6TJQfVP//8U+vWrWNWNYtlxbnctm2bLl26pGLFisne3l729vb6888/1b9/f/n6+mbJfuR0WXEeCxQoIHt7e5UvX96qT7ly5bgbcBbKinP5zz//6KOPPtLEiRPVokULVapUSb1799Zrr72m8ePHZ82O5HAZOY/ZMSYeLquPe+/evbVixQpt2rRJTz311COPB2uEVaSLg4ODqlevrg0bNljakpKStGHDBtWpUyfVderUqWPVX5LWrVtn6V+8eHF5enpa9YmJidHu3bvvOyYeTVacR+n/gmp4eLjWr1+v/PnzZ80OwCIrzmX79u11+PBhhYaGWl5eXl4aOHCg1qxZk3U7k4NlxXl0cHBQzZo1UzxK4eTJk/Lx8cnkPUCyrDiXCQkJSkhIUK5c1n9ts7Ozs8ygI3Nl5Dxmx5h4uKw67oZhqHfv3vrll1+0ceNGFS9ePDPKxb2y+QZP+A9auHChYTabjZCQEOPYsWNG9+7dDQ8PD+PChQuGYRhG+/btjQ8//NDSf8eOHYa9vb0xfvx4IywszPjkk09SfXSNh4eHsWzZMuPw4cNGy5YteXRNFsvs8xgfH2+8+OKLxlNPPWWEhoYakZGRlldcXFy27GNOkRW/k/fibsBZLyvO45IlS4zcuXMbX331lREeHm5MmzbNsLOzM7Zt2/bY9y8nyYpzGRgYaFSoUMHYtGmTcfr0aWPOnDmGo6OjMXPmzMe+fzlFes9jXFyccfDgQePgwYNGkSJFjAEDBhgHDx40wsPD0zwmskZWnMu3337bcHd3NzZv3mz1d55bt2499v17khFWkSHTpk0zihUrZjg4OBi1atUydu3aZVkWGBhodOzY0ar/Tz/9ZJQuXdpwcHAwKlSoYPz2229Wy5OSkowhQ4YYhQsXNsxms/Hss88aJ06ceBy7kqNl5nk8c+aMISnV16ZNmx7THuVcmf07eS/C6uORFefxm2++MUqVKmU4OjoalStXNpYuXZrVuwEj889lZGSkERwcbHh5eRmOjo5GmTJljAkTJhhJSUmPY3dyrPScx/v9fzAwMDDNYyLrZPa5vN/feebMmfP4dioHMBmGYTzOmVwAAAAAAB6G76wCAAAAAGwOYRUAAAAAYHMIqwAAAAAAm0NYBQAAAADYHMIqAAAAAMDmEFYBAAAAADaHsAoAAAAAsDmEVQAAAACAzSGsAgAAAABsDmEVAIAnTHBwsFq1apXdZaQqIiJCJpNJoaGh2V0KAMDGEVYBAMBjER8fn90lAAD+QwirAAA8wRo2bKg+ffqoX79+yps3rwoXLqyvv/5aN2/eVKdOneTq6qpSpUpp1apVlnU2b94sk8mk3377TZUqVZKjo6OefvppHT161GrsxYsXq0KFCjKbzfL19dWECROslvv6+mrkyJHq0KGD3Nzc1L17dxUvXlySVLVqVZlMJjVs2FCStHfvXjVp0kQFChSQu7u7AgMDdeDAAavxTCaTZs+erZdeekl58uSRn5+fli9fbtXnjz/+0AsvvCA3Nze5urqqfv36OnXqlGX57NmzVa5cOTk6Oqps2bKaOXPmIx9jAEDWIKwCAPCEmzt3rgoUKKA9e/aoT58+evvtt/Xqq6+qbt26OnDggJ577jm1b99et27dslpv4MCBmjBhgvbu3auCBQuqRYsWSkhIkCTt379fbdq00euvv64jR45o2LBhGjJkiEJCQqzGGD9+vCpXrqyDBw9qyJAh2rNnjyRp/fr1ioyM1JIlSyRJN27cUMeOHbV9+3bt2rVLfn5+at68uW7cuGE13vDhw9WmTRsdPnxYzZs3V7t27XTt2jVJ0vnz59WgQQOZzWZt3LhR+/fvV+fOnXXnzh1J0vz58zV06FB9+umnCgsL0+jRozVkyBDNnTs30485ACATGAAA4InSsWNHo2XLloZhGEZgYKBRr149y7I7d+4Yzs7ORvv27S1tkZGRhiRj586dhmEYxqZNmwxJxsKFCy19rl69ajg5ORk//vijYRiG8cYbbxhNmjSx2u7AgQON8uXLW977+PgYrVq1supz5swZQ5Jx8ODBB+5DYmKi4erqavz666+WNknG4MGDLe9jY2MNScaqVasMwzCMQYMGGcWLFzfi4+NTHbNkyZLGDz/8YNU2cuRIo06dOg+sBQCQPZhZBQDgCVepUiXLz3Z2dsqfP7/8/f0tbYULF5YkXbp0yWq9OnXqWH7Oly+fypQpo7CwMElSWFiYAgICrPoHBAQoPDxciYmJlrYaNWqkqcaLFy+qW7du8vPzk7u7u9zc3BQbG6uzZ8/ed1+cnZ3l5uZmqTs0NFT169dX7ty5U4x/8+ZNnTp1Sl26dJGLi4vlNWrUKKvLhAEAtsM+uwsAAABZ697wZjKZrNpMJpMkKSkpKdO37ezsnKZ+HTt21NWrVzVlyhT5+PjIbDarTp06KW7KlNq+JNft5OR03/FjY2MlSV9//bVq165ttczOzi5NNQIAHi/CKgAASNWuXbtUrFgxSdL169d18uRJlStXTpJUrlw57dixw6r/jh07VLp06QeGPwcHB0mymn1NXnfmzJlq3ry5JOncuXO6cuVKuuqtVKmS5s6dq4SEhBShtnDhwvLy8tLp06fVrl27dI0LAMgehFUAAJCqESNGKH/+/CpcuLA+/vhjFShQwPL81v79+6tmzZoaOXKkXnvtNe3cuVPTp09/6N11CxUqJCcnJ61evVpPPfWUHB0d5e7uLj8/P82bN081atRQTEyMBg4c+MCZ0tT07t1b06ZN0+uvv65BgwbJ3d1du3btUq1atVSmTBkNHz5cffv2lbu7u5o2baq4uDjt27dP169f13vvvZfRwwQAyCJ8ZxUAAKTqs88+0zvvvKPq1avrwoUL+vXXXy0zo9WqVdNPP/2khQsXqmLFiho6dKhGjBih4ODgB45pb2+vqVOn6ssvv5SXl5datmwpSfrmm290/fp1VatWTe3bt1ffvn1VqFChdNWbP39+bdy4UbGxsQoMDFT16tX19ddfW2ZZu3btqtmzZ2vOnDny9/dXYGCgQkJCLI/TAQDYFpNhGEZ2FwEAAGzH5s2b1ahRI12/fl0eHh7ZXQ4AIIdiZhUAAAAAYHMIqwAAAAAAm8NlwAAAAAAAm8PMKgAAAADA5hBWAQAAAAA2h7AKAAAAALA5hFUAAAAAgM0hrAIAAAAAbA5hFQAAAABgcwirAAAAAACbQ1gFAAAAANic/weY0yJjrwfDIQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, classification_report\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 1. Prepare data\n", "X = df_clean.drop(\"Potability\", axis=1)\n", "y = df_clean[\"Potability\"]\n", "\n", "# 2. Train/test split\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "\n", "# 3. <PERSON>\n", "rf_model = RandomForestClassifier(n_estimators=100, max_depth=None, random_state=42)\n", "rf_model.fit(X_train, y_train)\n", "\n", "# 4. Predict and evaluate\n", "y_pred = rf_model.predict(X_test)\n", "accuracy = accuracy_score(y_test, y_pred)\n", "f1 = f1_score(y_test, y_pred)\n", "roc = roc_auc_score(y_test, rf_model.predict_proba(X_test)[:, 1])\n", "\n", "print(\"🔍 Random Forest Performance:\")\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(f\"F1 Score: {f1:.4f}\")\n", "print(f\"ROC AUC: {roc:.4f}\")\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred))\n", "\n", "# 5. Feature importances\n", "importances = rf_model.feature_importances_\n", "feat_imp = pd.DataFrame({\"Feature\": X.columns, \"Importance\": importances})\n", "feat_imp = feat_imp.sort_values(\"Importance\", ascending=False)\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 5))\n", "sns.barplot(x=\"Importance\", y=\"Feature\", data=feat_imp)\n", "plt.title(\"🔬 Feature Importances – Random Forest\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "98a4b4bb", "metadata": {}, "source": ["Train XGBoost Model"]}, {"cell_type": "code", "execution_count": 8, "id": "383e9481", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚡ XGBoost Performance:\n", "Accuracy: 0.6585\n", "F1 Score: 0.3978\n", "ROC AUC: 0.6427\n", "\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.66      0.90      0.76       400\n", "           1       0.64      0.29      0.40       256\n", "\n", "    accuracy                           0.66       656\n", "   macro avg       0.65      0.59      0.58       656\n", "weighted avg       0.65      0.66      0.62       656\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\xgboost\\training.py:183: UserWarning: [00:23:41] WARNING: C:\\actions-runner\\_work\\xgboost\\xgboost\\src\\learner.cc:738: \n", "Parameters: { \"use_label_encoder\" } are not used.\n", "\n", "  bst.update(dtrain, iteration=i, fobj=obj)\n"]}, {"data": {"text/plain": ["<Figure size 1000x600 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import xgboost as xgb\n", "from xgboost import plot_importance\n", "\n", "# 1. Create XGBoost classifier\n", "xgb_model = xgb.XGBClassifier(\n", "    n_estimators=100,\n", "    max_depth=4,\n", "    learning_rate=0.1,\n", "    random_state=42,\n", "    use_label_encoder=False,\n", "    eval_metric='logloss'\n", ")\n", "\n", "# 2. <PERSON>\n", "xgb_model.fit(X_train, y_train)\n", "\n", "# 3. Predict and evaluate\n", "y_pred_xgb = xgb_model.predict(X_test)\n", "accuracy = accuracy_score(y_test, y_pred_xgb)\n", "f1 = f1_score(y_test, y_pred_xgb)\n", "roc = roc_auc_score(y_test, xgb_model.predict_proba(X_test)[:, 1])\n", "\n", "print(\"⚡ XGBoost Performance:\")\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(f\"F1 Score: {f1:.4f}\")\n", "print(f\"ROC AUC: {roc:.4f}\")\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred_xgb))\n", "\n", "# 4. Feature importance\n", "plt.figure(figsize=(10, 6))\n", "plot_importance(xgb_model, importance_type='gain', max_num_features=10)\n", "plt.title(\"📊 XGBoost Feature Importances\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "de468192", "metadata": {}, "source": ["First Uncertainty‑Aware Model — Quantile Regression Forest (QRF)"]}, {"cell_type": "code", "execution_count": 9, "id": "833408a0", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'RealNotInt' from 'sklearn.utils._param_validation' (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\utils\\_param_validation.py)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[1;32mIn[9], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mquantile_forest\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m RandomForestQuantileRegressor\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmetrics\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (accuracy_score, f1_score,\n\u001b[0;32m      4\u001b[0m                              roc_auc_score, brier_score_loss)\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01ms<PERSON>arn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcalibration\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m calibration_curve\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\quantile_forest\\__init__.py:26\u001b[0m\n\u001b[0;32m     22\u001b[0m     sys\u001b[38;5;241m.\u001b[39mstderr\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPartial import of quantile-forest during the build process.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     23\u001b[0m     \u001b[38;5;66;03m# We are not importing the rest of quantile-forest during the build\u001b[39;00m\n\u001b[0;32m     24\u001b[0m     \u001b[38;5;66;03m# process, as it may not be compiled yet\u001b[39;00m\n\u001b[0;32m     25\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m---> 26\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_quantile_forest\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ExtraTreesQuantileRegressor, RandomForestQuantileRegressor\n\u001b[0;32m     28\u001b[0m     __all__ \u001b[38;5;241m=\u001b[39m [\n\u001b[0;32m     29\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExtraTreesQuantileRegressor\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     30\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRandomForestQuantileRegressor\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     31\u001b[0m     ]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\quantile_forest\\_quantile_forest.py:41\u001b[0m\n\u001b[0;32m     39\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DecisionTreeRegressor, ExtraTreeRegressor\n\u001b[0;32m     40\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtree\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_tree\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DTYPE\n\u001b[1;32m---> 41\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_param_validation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Interval, RealNotInt\n\u001b[0;32m     42\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfixes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m parse_version\n\u001b[0;32m     43\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msklearn\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvalidation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m check_is_fitted\n", "\u001b[1;31mImportError\u001b[0m: cannot import name 'RealNotInt' from 'sklearn.utils._param_validation' (c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\sklearn\\utils\\_param_validation.py)"]}], "source": ["import numpy as np\n", "from quantile_forest import RandomForestQuantileRegressor\n", "from sklearn.metrics import (accuracy_score, f1_score,\n", "                             roc_auc_score, brier_score_loss)\n", "from sklearn.calibration import calibration_curve\n", "import matplotlib.pyplot as plt\n", "\n", "# 1️⃣  Train the model (same as before)\n", "qrf_model = RandomForestQuantileRegressor(\n", "    n_estimators=200,\n", "    max_depth=8,\n", "    random_state=42,\n", "    min_samples_leaf=5\n", ")\n", "qrf_model.fit(X_train.values, y_train_prob)\n", "\n", "# 2️⃣  Predict 5%, 50%, 95% quantiles **all at once**\n", "quantiles = [0.05, 0.5, 0.95]\n", "pred_mat  = qrf_model.predict(X_test.values, quantiles=quantiles)  # shape (n_samples, 3)\n", "\n", "# Slice out each column\n", "lower_05     = pred_mat[:, 0]\n", "median_pred  = pred_mat[:, 1]\n", "upper_95     = pred_mat[:, 2]\n", "\n", "# 3️⃣  Convert median prediction to class label (unsafe prob ≥ 0.5 ⇒ class 0)\n", "y_pred_qrf = (median_pred >= 0.5).astype(int)\n", "\n", "# 4️⃣  Evaluate\n", "acc_qrf   = accuracy_score(y_test, y_pred_qrf)\n", "f1_qrf    = f1_score(y_test, y_pred_qrf)\n", "roc_qrf   = roc_auc_score(y_test_prob, median_pred)\n", "brier_qrf = brier_score_loss(y_test_prob, median_pred)\n", "\n", "print(\"🌲 Quantile Forest (Uncertainty‑Aware) Results\")\n", "print(f\"Accuracy : {acc_qrf:.4f}\")\n", "print(f\"F1 Score : {f1_qrf:.4f}\")\n", "print(f\"ROC AUC  : {roc_qrf:.4f}\")\n", "print(f\"Brier    : {brier_qrf:.4f}  (↓ better means better calibration)\")\n", "\n", "# 5️⃣  Reliability diagram\n", "prob_true, prob_pred = calibration_curve(y_test_prob, median_pred, n_bins=10)\n", "\n", "plt.figure(figsize=(6,6))\n", "plt.plot(prob_pred, prob_true, marker='o', label='QRF')\n", "plt.plot([0,1],[0,1], linestyle='--', color='gray')\n", "plt.xlabel(\"Predicted probability of UNSAFE\")\n", "plt.ylabel(\"Observed frequency of UNSAFE\")\n", "plt.title(\"📏 Reliability Diagram – QRF\")\n", "plt.legend()\n", "plt.grid()\n", "plt.show()\n", "\n", "# 6️⃣  Prediction-interval plot (random 15 samples)\n", "sample_idx = np.random.choice(len(X_test), size=15, replace=False)\n", "plt.figure(figsize=(10,4))\n", "plt.errorbar(range(15), median_pred[sample_idx],\n", "             yerr=[median_pred[sample_idx]-lower_05[sample_idx],\n", "                   upper_95[sample_idx]-median_pred[sample_idx]],\n", "             fmt='o', capsize=5)\n", "plt.xticks(range(15))\n", "plt.ylabel(\"Predicted P(unsafe)\")\n", "plt.title(\"🔍 5–95 % Confidence Intervals for Random Test Samples\")\n", "plt.grid()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "fa835d57", "metadata": {}, "source": ["Takeaway:-Traditional quantile forests tend to produce collapsed intervals on noisy, low-resolution rural water data — motivating the need for smarter uncertainty estimators"]}, {"cell_type": "markdown", "id": "3bb5f436", "metadata": {}, "source": ["XGBoost & RF → higher accuracy but no confidence intervals\n", "\n", "QRF → lower accuracy, but calibrated probabilities + confidence intervals"]}, {"cell_type": "markdown", "id": "e3ca9262", "metadata": {}, "source": ["Train NGBoost – Probabilistic Classifier"]}, {"cell_type": "code", "execution_count": 10, "id": "4dcea5ad", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "check_array() got an unexpected keyword argument 'ensure_all_finite'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Cell \u001b[1;32mIn[10], line 32\u001b[0m\n\u001b[0;32m     29\u001b[0m ngb_model\u001b[38;5;241m.\u001b[39mfit(X_train, y_train)\n\u001b[0;32m     31\u001b[0m \u001b[38;5;66;03m# 3️⃣ Predict probability for \"unsafe\" (i.e., 1 - P(safe))\u001b[39;00m\n\u001b[1;32m---> 32\u001b[0m y_proba_ngb \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;241m-\u001b[39m \u001b[43mngb_model\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpredict_proba\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX_test\u001b[49m\u001b[43m)\u001b[49m[:, \u001b[38;5;241m1\u001b[39m]\n\u001b[0;32m     34\u001b[0m \u001b[38;5;66;03m# 4️⃣ Convert probabilities to binary prediction\u001b[39;00m\n\u001b[0;32m     35\u001b[0m y_pred_ngb \u001b[38;5;241m=\u001b[39m (y_proba_ngb \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.5\u001b[39m)\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mint\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\ngboost\\api.py:192\u001b[0m, in \u001b[0;36mNGBClassifier.predict_proba\u001b[1;34m(self, X, max_iter)\u001b[0m\n\u001b[0;32m    181\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpredict_proba\u001b[39m(\u001b[38;5;28mself\u001b[39m, X, max_iter\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    182\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    183\u001b[0m \u001b[38;5;124;03m    Probability prediction of Y at the points X=x\u001b[39;00m\n\u001b[0;32m    184\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    190\u001b[0m \u001b[38;5;124;03m        Numpy array of the estimates of P(Y=k|X=x). Will have shape (n, K)\u001b[39;00m\n\u001b[0;32m    191\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 192\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpred_dist\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_iter\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmax_iter\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mclass_probs()\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\ngboost\\ngboost.py:541\u001b[0m, in \u001b[0;36mNGBoost.pred_dist\u001b[1;34m(self, X, max_iter)\u001b[0m\n\u001b[0;32m    528\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpred_dist\u001b[39m(\u001b[38;5;28mself\u001b[39m, X, max_iter\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    529\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m    530\u001b[0m \u001b[38;5;124;03m    Predict the conditional distribution of Y at the points X=x\u001b[39;00m\n\u001b[0;32m    531\u001b[0m \n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    538\u001b[0m \u001b[38;5;124;03m        A NGBoost distribution object\u001b[39;00m\n\u001b[0;32m    539\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m--> 541\u001b[0m     X \u001b[38;5;241m=\u001b[39m \u001b[43mcheck_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43mX\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maccept_sparse\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mensure_all_finite\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mallow-nan\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m    543\u001b[0m     params \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39masarray(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpred_param(X, max_iter))\n\u001b[0;32m    544\u001b[0m     dist \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mDist(params\u001b[38;5;241m.\u001b[39mT)\n", "\u001b[1;31mTypeError\u001b[0m: check_array() got an unexpected keyword argument 'ensure_all_finite'"]}], "source": ["from ngboost import NGBClassifier\n", "from ngboost.distns import <PERSON><PERSON><PERSON>\n", "from sklearn.metrics import accuracy_score, f1_score, roc_auc_score, brier_score_loss\n", "from sklearn.calibration import calibration_curve\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 🧩 Final Fix: <PERSON>'s internal check_X_y\n", "import ngboost.ngboost as ngb_module\n", "from sklearn.utils.validation import check_X_y as sklearn_check_X_y\n", "\n", "def patched_check_X_y(X, y, **kwargs):\n", "    kwargs.pop('ensure_all_finite', None)\n", "    return sklearn_check_X_y(X, y, **kwargs)\n", "\n", "ngb_module.check_X_y = patched_check_X_y  # 🩹 Inject our patch\n", "\n", "# 1️⃣ Initialize NGBoost Classifier with <PERSON><PERSON><PERSON> distribution\n", "ngb_model = NGBClassifier(\n", "    Dist=<PERSON><PERSON><PERSON>,\n", "    n_estimators=500,\n", "    learning_rate=0.03,\n", "    natural_gradient=True,\n", "    verbose=False,\n", "    random_state=42\n", ")\n", "\n", "# 2️⃣ Fit the model\n", "ngb_model.fit(X_train, y_train)\n", "\n", "# 3️⃣ Predict probability for \"unsafe\" (i.e., 1 - P(safe))\n", "y_proba_ngb = 1 - ngb_model.predict_proba(X_test)[:, 1]\n", "\n", "# 4️⃣ Convert probabilities to binary prediction\n", "y_pred_ngb = (y_proba_ngb >= 0.5).astype(int)\n", "\n", "# 5️⃣ Evaluate performance\n", "acc_ngb   = accuracy_score(y_test, y_pred_ngb)\n", "f1_ngb    = f1_score(y_test, y_pred_ngb)\n", "roc_ngb   = roc_auc_score(1 - y_test.values, y_proba_ngb)\n", "brier_ngb = brier_score_loss(1 - y_test.values, y_proba_ngb)\n", "\n", "print(\"🚀 NGBoost (Bernoulli Probabilistic Classifier)\")\n", "print(f\"Accuracy  : {acc_ngb:.4f}\")\n", "print(f\"F1 Score  : {f1_ngb:.4f}\")\n", "print(f\"ROC AUC   : {roc_ngb:.4f}\")\n", "print(f\"Brier     : {brier_ngb:.4f}  (↓ lower is better)\")\n", "\n", "# 6️⃣ Reliability Diagram\n", "prob_true, prob_pred = calibration_curve(1 - y_test.values, y_proba_ngb, n_bins=10)\n", "\n", "plt.figure(figsize=(6,6))\n", "plt.plot(prob_pred, prob_true, marker='o', label='NGBoost')\n", "plt.plot([0, 1], [0, 1], linestyle='--', color='gray')\n", "plt.xlabel(\"Predicted Probability (UNSAFE)\")\n", "plt.ylabel(\"Empirical Probability (UNSAFE)\")\n", "plt.title(\"📐 Reliability Diagram – NGBoost\")\n", "plt.grid()\n", "plt.legend()\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}